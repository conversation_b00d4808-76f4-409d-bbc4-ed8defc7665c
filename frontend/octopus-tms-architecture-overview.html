<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Octopus TMS - Architecture, Module Design & Action Plan</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body { font-family: 'Inter', sans-serif; }
    .chart-container { position: relative; width: 100%; max-width: 700px; margin-left: auto; margin-right: auto; height: 400px; max-height: 450px; }
    @media (max-width: 768px) {
      .chart-container { height: 300px; max-height: 350px; }
    }
    .tab-content { display: none; }
    .tab-content.active { display: block; }
    .nav-link { transition: color 0.3s ease; }
    .nav-link:hover { color: #2563eb; }
    .card { transition: transform 0.3s ease, box-shadow 0.3s ease; }
    .card:hover { transform: translateY(-5px); box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05); }
    .modal { display: none; position: fixed; z-index: 50; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.6); }
    .modal-content { background-color: #fefefe; margin: 10% auto; padding: 20px; border-radius: 0.5rem; width: 90%; max-width: 600px; }
    .tooltip-custom { position:absolute; background:rgba(0,0,0,0.7); color:white; padding:5px; border-radius:3px; font-size:0.875rem; pointer-events:none; opacity:0; transition:opacity 0.2s; z-index:100; }
    .spinner { border: 4px solid rgba(0, 0, 0, 0.1); border-left-color: #2563eb; border-radius: 50%; width: 24px; height: 24px; animation: spin 1s linear infinite; display: inline-block; vertical-align: middle; margin-left: 8px; }
    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    pre { background-color: #f8f8f8; padding: 1rem; border-radius: 0.5rem; overflow-x: auto; }
    code { font-family: 'Courier New', monospace; }
    table { width: 100%; border-collapse: collapse; margin-bottom: 1rem; }
    th, td { padding: 0.75rem; text-align: left; border: 1px solid #e2e8f0; }
    th { background-color: #f8fafc; }
    tr:nth-child(even) { background-color: #f8fafc; }
  </style>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-stone-100 text-stone-800">

<header class="bg-white shadow-md sticky top-0 z-40">
  <nav class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex items-center justify-between h-16">
      <div class="flex items-center">
        <a href="../OCTI-docs/index.html" class="font-bold text-xl text-sky-700">Octopus TMS Documentation</a>
      </div>
      <div class="hidden md:block">
        <div class="ml-10 flex items-baseline space-x-4" id="desktop-nav">
          <a href="../OCTI-docs/index.html" class="nav-link px-3 py-2 rounded-md text-sm font-medium text-stone-600 hover:text-sky-600 hover:bg-stone-50">Home</a>
          <a href="../OCTI-docs/octopus-tms-system-plan.html" class="nav-link px-3 py-2 rounded-md text-sm font-medium text-stone-600 hover:text-sky-600 hover:bg-stone-50">System Plan</a>
          <a href="../OCTI-docs/octopus-tms-documentation.html" class="nav-link px-3 py-2 rounded-md text-sm font-medium text-stone-600 hover:text-sky-600 hover:bg-stone-50">Technical Documentation</a>
          <a href="../OCTI-docs/Backend_Implementation_Plan.html" class="nav-link px-3 py-2 rounded-md text-sm font-medium text-stone-600 hover:text-sky-600 hover:bg-stone-50">Backend Plan</a>
          <a href="octopus-tms-architecture-overview.html" class="nav-link px-3 py-2 rounded-md text-sm font-medium text-sky-600 border-b-2 border-sky-500">Architecture Overview</a>
        </div>
      </div>
      <div class="md:hidden">
        <button id="mobile-menu-button" class="inline-flex items-center justify-center p-2 rounded-md text-stone-500 hover:text-stone-700 hover:bg-stone-200 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-sky-500">
          <span class="sr-only">Open main menu</span>
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" /></svg>
        </button>
      </div>
    </div>
    <div id="mobile-menu" class="md:hidden hidden">
      <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3" id="mobile-nav">
        <a href="../OCTI-docs/index.html" class="block nav-link px-3 py-2 rounded-md text-base font-medium text-stone-600 hover:text-sky-600 hover:bg-stone-50">Home</a>
        <a href="../OCTI-docs/octopus-tms-system-plan.html" class="block nav-link px-3 py-2 rounded-md text-base font-medium text-stone-600 hover:text-sky-600 hover:bg-stone-50">System Plan</a>
        <a href="../OCTI-docs/octopus-tms-documentation.html" class="block nav-link px-3 py-2 rounded-md text-base font-medium text-stone-600 hover:text-sky-600 hover:bg-stone-50">Technical Documentation</a>
        <a href="../OCTI-docs/Backend_Implementation_Plan.html" class="block nav-link px-3 py-2 rounded-md text-base font-medium text-stone-600 hover:text-sky-600 hover:bg-stone-50">Backend Plan</a>
        <a href="octopus-tms-architecture-overview.html" class="block nav-link px-3 py-2 rounded-md text-base font-medium text-sky-600 bg-stone-50">Architecture Overview</a>
      </div>
    </div>
  </nav>
</header>

<main class="container mx-auto p-4 sm:p-6 lg:p-8">
  <div id="content" class="bg-white p-6 sm:p-8 rounded-lg shadow-lg my-8">
    <h1 class="text-4xl font-bold text-sky-700 mb-6">🐙 Octopus TMS – Architecture, Module Design & Action Plan</h1>
    
    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
      <p class="text-yellow-700 font-semibold">IMPORTANT: This is a mock/UI only for the frontend of the TMS application, for DEMO purposes only for now. The backend is not yet implemented, and the frontend is not yet connected to the backend.</p>
    </div>

    <section id="project-history" class="mb-8">
      <h2 class="text-2xl font-bold text-stone-700 mb-4">🕰️ Project History</h2>
      
      <p class="text-stone-600 mb-4 leading-relaxed">Octopus TMS started as a modern, modular TMS (Transportation Management System) designed to serve logistics companies. Its architecture is centered around <strong>role-based modules</strong>, beginning with a <strong>Carrier module</strong> and later expanded with a full-featured <strong>Broker module</strong>.</p>
      
      <ul class="list-disc list-inside space-y-1 text-stone-600 ml-4">
        <li><strong>Initial Scope:</strong> Carrier-side features only (load tracking, documents, check-ins, etc.)</li>
        <li><strong>New Additions:</strong> Broker module built by "Junie" — covering load creation, carrier matching, contracts, payments, and docs.</li>
        <li>Built using <strong>React + Vite + TypeScript + TailwindCSS</strong> with modular routing and shared context for authentication and layout.</li>
      </ul>
    </section>

    <hr class="my-6 border-t border-stone-200">

    <section id="technical-overview" class="mb-8">
      <h2 class="text-2xl font-bold text-stone-700 mb-4">⚙️ Technical Overview</h2>
      
      <h3 class="text-xl font-semibold text-stone-700 mb-3">🧰 Frontend Tools & Libraries</h3>
      <ul class="list-disc list-inside space-y-1 text-stone-600 ml-4">
        <li><strong>Framework</strong>: React 18.2.0</li>
        <li><strong>Language</strong>: TypeScript 5.0.4</li>
        <li><strong>Build Tool</strong>: Vite 4.4.5</li>
        <li><strong>CSS Framework</strong>: Tailwind CSS 3.3.3</li>
        <li><strong>Routing</strong>: React Router DOM 6.15.0</li>
        <li><strong>HTTP Client</strong>: Axios 1.6.2</li>
        <li><strong>State Management</strong>: React Context API</li>
        <li><strong>UI Components</strong>: Custom components with Tailwind CSS</li>
      </ul>
      
      <h3 class="text-xl font-semibold text-stone-700 mb-3 mt-6">Integrations</h3>
      <ul class="list-disc list-inside space-y-1 text-stone-600 ml-4">
        <li><strong>Charts</strong>: ApexCharts 3.44.0 with React ApexCharts 1.4.1</li>
        <li><strong>Maps</strong>: Leaflet 1.9.4 with React Leaflet 4.2.1</li>
        <li><strong>PDF Handling</strong>: React PDF 7.5.1 and @react-pdf/renderer 3.1.14</li>
        <li><strong>Weather API</strong>: OpenWeatherMap API</li>
        <li><strong>Notifications</strong>: React Hot Toast 2.4.1</li>
      </ul>
      
      <h3 class="text-xl font-semibold text-stone-700 mb-3 mt-6">Backend (Planned)</h3>
      <ul class="list-disc list-inside space-y-1 text-stone-600 ml-4">
        <li><strong>Framework</strong>: Spring Boot (Java 21)</li>
        <li><strong>Database</strong>: PostgreSQL</li>
        <li><strong>Build Tool</strong>: Gradle with Kotlin DSL</li>
        <li><strong>API</strong>: RESTful API</li>
      </ul>
    </section>

    <section id="project-configuration" class="mb-8">
      <h2 class="text-2xl font-bold text-stone-700 mb-4">Project Configuration</h2>
      
      <h3 class="text-xl font-semibold text-stone-700 mb-3">Vite Configuration</h3>
      <p class="text-stone-600 mb-4">The application uses Vite as its build tool with the following configuration:</p>
      <ul class="list-disc list-inside space-y-1 text-stone-600 ml-4">
        <li>Development server runs on port 3000</li>
        <li>API requests are proxied to the backend server on port 8080</li>
        <li>Production build outputs to the backend's static resources directory</li>
      </ul>
      
      <pre><code>// vite.config.ts
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    proxy: {
      '/api': 'http://localhost:8080'
    }
  },
  build: {
    outDir: '../backend/src/main/resources/static',
    emptyOutDir: true
  }
})</code></pre>
      
      <h3 class="text-xl font-semibold text-stone-700 mb-3 mt-6">TypeScript Configuration</h3>
      <p class="text-stone-600 mb-4">The TypeScript configuration targets ES2020 and uses strict type checking:</p>
      
      <pre><code>// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "strict": true,
    "jsx": "react-jsx",
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  }
}</code></pre>
      
      <h3 class="text-xl font-semibold text-stone-700 mb-3 mt-6">Tailwind CSS Configuration</h3>
      <p class="text-stone-600 mb-4">Tailwind CSS is configured with custom colors and fonts:</p>
      
      <pre><code>// tailwind.config.ts
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}"
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Poppins', 'sans-serif'],
      },
      colors: {
        primary: {
            '50': '#f0f9ff',
            '100': '#e0f2fe',
            '200': '#bae6fd',
            '300': '#7dd3fc',
            '400': '#38bdf8',
            '500': '#0ea5e9',
            '600': '#0284c7',
            '700': '#0369a1',
            '800': '#075985',
            '900': '#0c4a6e',
        },
      },
    },
  },
    plugins: [],
} satisfies Config</code></pre>
    </section>

    <section id="project-structure" class="mb-8">
      <h2 class="text-2xl font-bold text-stone-700 mb-4">Project Structure</h2>
      
      <h3 class="text-xl font-semibold text-stone-700 mb-3">📁 File Structure (Current & Proposed Clean Structure)</h3>
      <pre><code>frontend/
├── src/
│   ├── components/         # Shared UI components (not role-specific)
│   ├── context/            # AuthContext, RoleContext
│   ├── layouts/            # Shared layouts, Topbar, Sidebar
│   ├── modules/
│   │   ├── carrier/        # Carrier-only features
│   │   └── broker/         # Broker-only features
│   ├── routes/             # Merged + modular route definitions
│   ├── services/           # API clients, utils, external integrations
│   ├── types/              # Global enums, interfaces
│   ├── utils/              # Shared helpers (formatters, validators)
│   ├── App.tsx
│   └── main.tsx
├── README.md
└── vite.config.ts</code></pre>
    </section>

    <hr class="my-6 border-t border-stone-200">

    <section id="business-functionalities" class="mb-8">
      <h2 class="text-2xl font-bold text-stone-700 mb-4">💼 Business Functionalities</h2>
      
      <h3 class="text-xl font-semibold text-stone-700 mb-3">🚛 Carrier Module</h3>
      
      <p class="mb-4"><strong>User Type:</strong> Dispatcher, Driver, Supervisor</p>
      <p class="mb-4"><strong>Key Features:</strong></p>
      <ul class="list-disc list-inside space-y-1 text-stone-600 ml-4">
        <li>Load status tracking</li>
        <li>Labor ticket interface (Check-in, Team, Images, Progress)</li>
        <li>Chat, calendar, estimate approvals</li>
        <li>Role-based UI (Dispatcher vs. Crew)</li>
        <li>Image/document upload, weather widgets, crew coordination</li>
      </ul>
      
      <p class="mt-4 mb-2"><strong>UI Behavior:</strong></p>
      <ul class="list-disc list-inside space-y-1 text-stone-600 ml-4">
        <li>Sticky footer menu for mobile</li>
        <li>Large active job card with progress bar</li>
        <li>Permission-based route access</li>
      </ul>
      
      <h4 class="text-lg font-semibold text-stone-700 mb-3 mt-6">Load Management</h4>
      <p class="text-stone-600 mb-4">The application provides functionality for managing loads:</p>
      <ul class="list-disc list-inside space-y-1 text-stone-600 ml-4">
        <li><strong>Smart Load Search</strong>: Search for available loads based on criteria</li>
        <li><strong>Dispatch Board</strong>: Assign drivers to loads and track load status</li>
        <li><strong>Tracking</strong>: Monitor real-time location of shipments</li>
        <li><strong>Documents</strong>: Manage load-related documents (rate confirmations, BOLs, PODs)</li>
        <li><strong>Invoices</strong>: Generate and manage invoices for completed loads</li>
      </ul>
      
      <h4 class="text-lg font-semibold text-stone-700 mb-3 mt-6">Driver Management</h4>
      <p class="text-stone-600 mb-4">The application includes features for managing drivers:</p>
      <ul class="list-disc list-inside space-y-1 text-stone-600 ml-4">
        <li><strong>Driver List</strong>: View and manage driver information</li>
        <li><strong>Driver Assignment</strong>: Assign drivers to loads</li>
        <li><strong>Driver Performance</strong>: Track driver performance metrics</li>
      </ul>
      
      <h4 class="text-lg font-semibold text-stone-700 mb-3 mt-6">Weather Monitoring</h4>
      <p class="text-stone-600 mb-4">The application monitors weather conditions that may affect transportation:</p>
      <ul class="list-disc list-inside space-y-1 text-stone-600 ml-4">
        <li><strong>Weather Alerts</strong>: Display alerts for severe weather conditions</li>
        <li><strong>Impact Assessment</strong>: Assess the impact of weather on loads</li>
        <li><strong>Current Weather</strong>: Show current weather conditions for specific locations</li>
      </ul>
      
      <hr class="my-6 border-t border-stone-200">
      
      <h3 class="text-xl font-semibold text-stone-700 mb-3">📦 Broker Module (done by Junie)</h3>
      
      <p class="mb-4"><strong>User Type:</strong> Freight Broker</p>
      <p class="mb-4"><strong>Key Features:</strong></p>
      <ul class="list-disc list-inside space-y-1 text-stone-600 ml-4">
        <li>Create loads with pickup/drop details</li>
        <li>Simulated posting to DAT/Truckstop</li>
        <li>Carrier matching (lane search, rate benchmarks)</li>
        <li>Contract upload + status tracking</li>
        <li>Invoice + payment management</li>
        <li>Load document uploads (BOL, POD, etc.)</li>
      </ul>
      
      <p class="mt-4 mb-2"><strong>UI Behavior:</strong></p>
      <ul class="list-disc list-inside space-y-1 text-stone-600 ml-4">
        <li>Broker-only dashboard</li>
        <li>Topbar updates based on role</li>
        <li>Post-load flow: Draft → Posted → Assigned → Delivered → Paid</li>
        <li>Toast messages for actions (posting, submission)</li>
      </ul>
    </section>

    <hr class="my-6 border-t border-stone-200">

    <section id="architecture-analysis" class="mb-8">
      <h2 class="text-2xl font-bold text-stone-700 mb-4">✅ Architecture Analysis Findings (May 2025)</h2>
      
      <h3 class="text-xl font-semibold text-stone-700 mb-3">Key Accomplishments</h3>
      
      <h4 class="text-lg font-semibold text-stone-700 mb-3">1. Broker Module Implementation ✅</h4>
      <ul class="list-disc list-inside space-y-1 text-stone-600 ml-4">
        <li>Successfully implemented by Junie with clean isolation under <code>/src/modules/broker/</code></li>
        <li>Includes all necessary components: pages, components, hooks, API, and routes</li>
        <li>Proper role-based access control with dedicated <code>BrokerRoute</code> wrapper</li>
        <li>No broker logic leaked into carrier pages</li>
      </ul>
      
      <h4 class="text-lg font-semibold text-stone-700 mb-3 mt-4">2. Authentication & Role Management ✅</h4>
      <ul class="list-disc list-inside space-y-1 text-stone-600 ml-4">
        <li>Unified authentication system via <code>AuthContext</code></li>
        <li>Support for multiple roles: Operator, Dispatcher, Supervisor, Admin, Carrier, Broker</li>
        <li>Role-based routing and navigation updates in Topbar component</li>
        <li>Login redirects users based on role (brokers → <code>/broker/dashboard</code>, others → <code>/dashboard</code>)</li>
      </ul>
      
      <h4 class="text-lg font-semibold text-stone-700 mb-3 mt-4">3. Technical Implementation Details</h4>
      <ul class="list-disc list-inside space-y-1 text-stone-600 ml-4">
        <li><strong>Mock API Layer</strong>: All broker operations have mock API functions in <code>brokerApi.ts</code></li>
        <li><strong>Custom Hooks</strong>: Dedicated hooks for each broker feature (dashboard, loads, carrier matching, etc.)</li>
        <li><strong>UI Components</strong>: Reusable components like LoadCard, LoadStatusBadge, LoadProgressTracker</li>
        <li><strong>Toast Notifications</strong>: Integrated react-hot-toast for user feedback</li>
      </ul>
      
      <h3 class="text-xl font-semibold text-stone-700 mb-3 mt-6">How to Login</h3>
      <p class="text-stone-600 mb-4">Use these demo credentials:</p>
      <ul class="list-disc list-inside space-y-1 text-stone-600 ml-4">
        <li><strong>Broker</strong>: username: <code>broker1</code>, password: <code>password</code></li>
        <li><strong>Dispatcher</strong>: username: <code>dispatcher1</code>, password: <code>password</code></li>
        <li><strong>Supervisor</strong>: username: <code>supervisor1</code>, password: <code>password</code></li>
        <li><strong>Admin</strong>: username: <code>admin</code>, password: <code>password</code></li>
      </ul>
      
      <h3 class="text-xl font-semibold text-stone-700 mb-3 mt-6">Important Technical Functionalities</h3>
      
      <ol class="list-decimal list-inside space-y-3 text-stone-600 ml-4">
        <li>
          <strong>Load Board Integration (Simulated)</strong>
          <ul class="list-disc list-inside ml-6 mt-2">
            <li>CreateLoad component simulates posting to DAT/Truckstop</li>
            <li>Toggle switch to enable/disable load board posting</li>
            <li>Toast notifications confirm simulated posting</li>
          </ul>
        </li>
        <li>
          <strong>Carrier-Broker Interaction</strong>
          <ul class="list-disc list-inside ml-6 mt-2">
            <li><code>BrokerCallModal</code> and <code>LoadMatchModal</code> enable carriers to contact brokers</li>
            <li>Smart Load Search includes 10-second simulated search with notification</li>
            <li>Mock broker database with ratings and contact information</li>
          </ul>
        </li>
        <li>
          <strong>Document Management</strong>
          <ul class="list-disc list-inside ml-6 mt-2">
            <li>Separate implementations for carriers and brokers</li>
            <li>Carrier version: Full-featured with preview, sharing, storage tracking</li>
            <li>Broker version: Load-centric with carrier associations</li>
          </ul>
        </li>
        <li>
          <strong>Payment Workflow</strong>
          <ul class="list-disc list-inside ml-6 mt-2">
            <li>Three-stage process: Pending → Approved → Paid</li>
            <li>Simulated Triumph Pay integration</li>
            <li>Invoice management with filtering capabilities</li>
          </ul>
        </li>
      </ol>
      
      <h3 class="text-xl font-semibold text-stone-700 mb-3 mt-6">Architecture Issues & Recommendations</h3>
      
      <h4 class="text-lg font-semibold text-stone-700 mb-3">Issues Found:</h4>
      <ol class="list-decimal list-inside space-y-1 text-stone-600 ml-4">
        <li><strong>Asymmetric Module Structure</strong>: Broker has proper module structure, Carrier functionality is scattered in <code>/pages/</code></li>
        <li><strong>Cross-Role Components</strong>: Some components serve both carriers and brokers but live in shared space</li>
        <li><strong>No Explicit Carrier Module</strong>: Carrier features lack the clean organization of broker module</li>
      </ol>
      
      <h4 class="text-lg font-semibold text-stone-700 mb-3 mt-4">Recommended Refactoring:</h4>
      
      <p class="mb-2"><strong>Priority 1: Create Carrier Module</strong></p>
      <pre><code>src/modules/carrier/
├── pages/
├── components/
├── hooks/
├── api/
└── carrierRoutes.tsx</code></pre>
      
      <p class="mb-2 mt-4"><strong>Priority 2: Create Shared Module</strong></p>
      <pre><code>src/modules/shared/
└── components/
    ├── BrokerCallModal.tsx
    └── LoadMatchModal.tsx</code></pre>
      
      <p class="mb-2 mt-4"><strong>Priority 3: Standardize All Modules</strong></p>
      <ul class="list-disc list-inside space-y-1 text-stone-600 ml-4">
        <li>Consistent structure across all modules</li>
        <li>Clear separation of role-specific vs shared functionality</li>
        <li>Prepare for future Shipper and Admin modules</li>
      </ul>
      
      <h3 class="text-xl font-semibold text-stone-700 mb-3 mt-6">Next Steps for Future Development</h3>
      
      <ul class="space-y-1 text-stone-600 ml-4">
        <li><span class="text-green-500">✅</span> Implement carrier module refactoring - <strong>COMPLETED January 2025</strong></li>
        <li><span class="text-green-500">✅</span> Create shared module for cross-role components - <strong>COMPLETED January 2025</strong></li>
        <li>[ ] Add real-time notifications system</li>
        <li>[ ] Implement actual load board integrations</li>
        <li>[ ] Add WebSocket support for live updates</li>
        <li>[ ] Build Shipper module when needed</li>
        <li>[ ] Connect to real backend APIs</li>
      </ul>
      
      <h3 class="text-xl font-semibold text-stone-700 mb-3 mt-6">Refactoring Completed (January 2025)</h3>
      
      <h4 class="text-lg font-semibold text-stone-700 mb-3">Created Modular Structure:</h4>
      <pre><code>src/modules/
├── broker/          # Broker module (by Junie)
│   ├── api/
│   ├── components/
│   ├── hooks/
│   ├── pages/
│   └── brokerRoutes.tsx
├── carrier/         # Carrier module (refactored)
│   ├── api/
│   ├── hooks/
│   ├── pages/
│   └── carrierRoutes.tsx
└── shared/          # Cross-role components
    └── components/
        ├── BrokerCallModal.tsx
        └── LoadMatchModal.tsx</code></pre>
      
      <h4 class="text-lg font-semibold text-stone-700 mb-3 mt-4">Key Changes:</h4>
      <ol class="list-decimal list-inside space-y-1 text-stone-600 ml-4">
        <li><strong>Carrier Module Created</strong>: All carrier-specific pages moved from <code>/pages</code> to <code>/modules/carrier/pages</code></li>
        <li><strong>Shared Module Created</strong>: Cross-role components (BrokerCallModal, LoadMatchModal) moved to shared module</li>
        <li><strong>Clean Separation</strong>: Each module has its own API, routes, and components</li>
        <li><strong>Role-Based Access</strong>: Both modules implement proper role guards</li>
        <li><strong>Consistent Structure</strong>: Carrier module now matches broker module structure</li>
      </ol>
      
      <h4 class="text-lg font-semibold text-stone-700 mb-3 mt-4">Benefits:</h4>
      <ul class="list-disc list-inside space-y-1 text-stone-600 ml-4">
        <li><strong>Scalability</strong>: Easy to add new modules (Shipper, Admin)</li>
        <li><strong>Maintainability</strong>: Clear boundaries between modules</li>
        <li><strong>Reusability</strong>: Shared components for cross-role features</li>
        <li><strong>Type Safety</strong>: Proper TypeScript throughout</li>
      </ul>
    </section>
  </div>

  <section id="gemini-features" class="my-12 scroll-mt-20">
    <div class="bg-white p-6 sm:p-8 rounded-lg shadow-lg">
      <h2 class="text-3xl font-bold text-sky-700 mb-6">✨ AI-Powered Insights for Logistics ✨</h2>
      <p class="text-stone-600 mb-8 leading-relaxed">Leverage advanced AI to gain deeper insights and assistance with your logistics and API integration queries. These features are powered by the Gemini API.</p>

      <div class="grid md:grid-cols-2 gap-8">
        <div class="bg-stone-50 p-6 rounded-lg shadow-md">
          <h3 class="text-xl font-semibold text-stone-700 mb-4">AI Assistant</h3>
          <p class="text-stone-600 mb-4 text-sm">Ask a question about Octopus TMS or logistics in general.</p>
          <textarea id="apiAssistantInput" class="w-full p-3 border border-stone-300 rounded-md focus:outline-none focus:ring-2 focus:ring-sky-500 mb-4 text-stone-700" rows="4" placeholder="e.g., Explain how the Broker module works or 'What are the best practices for TMS implementation?'"></textarea>
          <button id="getApiAssistantResponse" class="bg-sky-600 text-white px-6 py-2 rounded-md hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 flex items-center justify-center">
            Get Insight ✨
            <span id="apiAssistantSpinner" class="spinner hidden"></span>
          </button>
          <div id="apiAssistantOutput" class="mt-6 p-4 bg-stone-100 rounded-md text-stone-700 border border-stone-200 min-h-[80px]"></div>
        </div>

        <div class="bg-stone-50 p-6 rounded-lg shadow-md">
          <h3 class="text-xl font-semibold text-stone-700 mb-4">Strategic Insight Generator</h3>
          <p class="text-stone-600 mb-4 text-sm">Generate strategic insights based on common industry scenarios.</p>
          <div class="flex flex-wrap gap-3 mb-4">
            <button class="strategic-prompt-button bg-teal-600 text-white px-4 py-2 rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 text-sm">
              TMS Implementation Best Practices ✨
            </button>
            <button class="strategic-prompt-button bg-teal-600 text-white px-4 py-2 rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 text-sm">
              Logistics Technology Trends ✨
            </button>
            <button class="strategic-prompt-button bg-teal-600 text-white px-4 py-2 rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 text-sm">
              Carrier-Broker Relationship Tips ✨
            </button>
          </div>
          <div id="strategicInsightOutput" class="mt-6 p-4 bg-stone-100 rounded-md text-stone-700 border border-stone-200 min-h-[80px]"></div>
          <span id="strategicInsightSpinner" class="spinner hidden"></span>
        </div>
      </div>
    </div>
  </section>
</main>

<footer class="bg-stone-800 text-stone-300 text-center p-6 mt-12">
  <p>&copy; <span id="currentYear"></span> Octopus TMS Documentation. All rights reserved.</p>
</footer>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    const currentYearElement = document.getElementById('currentYear');
    if (currentYearElement) {
      currentYearElement.textContent = new Date().getFullYear();
    }

    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    if (mobileMenuButton && mobileMenu) {
      mobileMenuButton.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
      });
    }

    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        if (this.hash !== "") {
          const targetElement = document.querySelector(this.hash);
          if (targetElement) {
            e.preventDefault();
            const offset = document.querySelector('header').offsetHeight || 0;
            const elementPosition = targetElement.getBoundingClientRect().top;
            const offsetPosition = elementPosition + window.pageYOffset - offset;

            window.scrollTo({
              top: offsetPosition,
              behavior: "smooth"
            });
            if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
              mobileMenu.classList.add('hidden');
            }
          }
        }
      });
    });

    async function callGeminiAPI(prompt, outputElement, spinnerElement) {
      outputElement.innerHTML = '';
      outputElement.classList.add('text-stone-500', 'italic');
      outputElement.textContent = 'Generating insight...';
      spinnerElement.classList.remove('hidden');

      try {
        let chatHistory = [];
        chatHistory.push({ role: "user", parts: [{ text: prompt }] });
        const payload = { contents: chatHistory };
        const apiKey = "AIzaSyBA9_8uM4d51AGwb55yPo3bICgyGxNBong";
        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload)
        });

        const result = await response.json();
        if (result.candidates && result.candidates.length > 0 &&
                result.candidates[0].content && result.candidates[0].content.parts &&
                result.candidates[0].content.parts.length > 0) {
          const text = result.candidates[0].content.parts[0].text;
          outputElement.innerHTML = text;
          outputElement.classList.remove('text-stone-500', 'italic');
        } else {
          outputElement.textContent = 'Error: Could not generate response. Please try again.';
          outputElement.classList.add('text-red-500', 'italic');
        }
      } catch (error) {
        console.error('Error calling Gemini API:', error);
        outputElement.textContent = 'Error: Failed to connect to AI. Please check your network or try again later.';
        outputElement.classList.add('text-red-500', 'italic');
      } finally {
        spinnerElement.classList.add('hidden');
      }
    }

    const apiAssistantInput = document.getElementById('apiAssistantInput');
    const getApiAssistantResponseButton = document.getElementById('getApiAssistantResponse');
    const apiAssistantOutput = document.getElementById('apiAssistantOutput');
    const apiAssistantSpinner = document.getElementById('apiAssistantSpinner');

    if (getApiAssistantResponseButton && apiAssistantInput && apiAssistantOutput && apiAssistantSpinner) {
      getApiAssistantResponseButton.addEventListener('click', () => {
        const prompt = apiAssistantInput.value.trim();
        if (prompt) {
          callGeminiAPI(prompt, apiAssistantOutput, apiAssistantSpinner);
        } else {
          apiAssistantOutput.textContent = 'Please enter a question for the AI Assistant.';
          apiAssistantOutput.classList.add('text-red-500', 'italic');
        }
      });
    }

    const strategicPromptButtons = document.querySelectorAll('.strategic-prompt-button');
    const strategicInsightOutput = document.getElementById('strategicInsightOutput');
    const strategicInsightSpinner = document.getElementById('strategicInsightSpinner');

    if (strategicPromptButtons.length > 0 && strategicInsightOutput && strategicInsightSpinner) {
      strategicPromptButtons.forEach(button => {
        button.addEventListener('click', () => {
          let prompt = "";
          if (button.textContent.includes("TMS Implementation Best Practices")) {
            prompt = "What are the best practices for implementing a Transportation Management System (TMS) in a logistics company?";
          } else if (button.textContent.includes("Logistics Technology Trends")) {
            prompt = "What are the current and emerging technology trends in the logistics and transportation industry?";
          } else if (button.textContent.includes("Carrier-Broker Relationship Tips")) {
            prompt = "What are the key strategies for building and maintaining successful carrier-broker relationships in the logistics industry?";
          }
          if (prompt) {
            callGeminiAPI(prompt, strategicInsightOutput, strategicInsightSpinner);
          }
        });
      });
    }
  });
</script>

</body>
</html>