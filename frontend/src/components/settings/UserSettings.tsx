import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import { User, Role } from '../../types/settings';
import ToggleSwitch from '../ToggleSwitch';

interface UserSettingsProps {
  initialUsers: User[];
  initialRoles: Role[];
}

const UserSettings: React.FC<UserSettingsProps> = ({ initialUsers, initialRoles }) => {
  const [users, setUsers] = useState<User[]>(initialUsers);
  const [roles, setRoles] = useState<Role[]>(initialRoles);
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [showEditUserModal, setShowEditUserModal] = useState(false);
  const [showAddRoleModal, setShowAddRoleModal] = useState(false);
  const [showEditRoleModal, setShowEditRoleModal] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [currentRole, setCurrentRole] = useState<Role | null>(null);

  // Users & Permissions handlers
  const handleAddUser = () => {
    setShowAddUserModal(true);
    toast.success('Add User modal would open here');
  };

  const handleEditUser = (user: User) => {
    setCurrentUser(user);
    setShowEditUserModal(true);
    toast.success(`Editing user: ${user.firstName} ${user.lastName}`);
  };

  const handleToggleUserStatus = (userId: string) => {
    setUsers(users.map(user => {
      if (user.id === userId) {
        const newStatus = user.status === 'active' ? 'inactive' : 'active';
        toast.success(`User ${user.firstName} ${user.lastName} ${newStatus === 'active' ? 'activated' : 'deactivated'}`);
        return { ...user, status: newStatus };
      }
      return user;
    }));
  };

  const handleAddRole = () => {
    setShowAddRoleModal(true);
    toast.success('Add Role modal would open here');
  };

  const handleEditRole = (role: Role) => {
    setCurrentRole(role);
    setShowEditRoleModal(true);
    toast.success(`Editing permissions for role: ${role.name}`);
  };

  return (
    <div>
      <div className="border-b border-gray-200 pb-4 mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Users & Permissions</h2>
        <p className="text-sm text-gray-600 mt-1">Manage users, roles, and access permissions</p>
      </div>

      {/* Users Section */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">Users</h3>
          <button 
            className="btn btn-sm btn-primary"
            onClick={handleAddUser}
          >
            <i className="fas fa-plus mr-2"></i> Add User
          </button>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Role
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Department
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Login
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {users.map(user => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className={`flex-shrink-0 h-10 w-10 rounded-full flex items-center justify-center text-white bg-${user.avatarColor}-500`}>
                          {user.initials}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {user.firstName} {user.lastName}
                          </div>
                          <div className="text-sm text-gray-500">
                            {user.email}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        user.role === 'Admin' ? 'bg-purple-100 text-purple-800' :
                        user.role === 'Dispatcher' ? 'bg-blue-100 text-blue-800' :
                        user.role === 'Accountant' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {user.role}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {user.department}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {user.lastLogin}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <ToggleSwitch 
                        isOn={user.status === 'active'} 
                        onToggle={() => handleToggleUserStatus(user.id)}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button 
                        className="text-blue-600 hover:text-blue-900 mr-3"
                        onClick={() => handleEditUser(user)}
                      >
                        Edit
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Roles Section */}
      <div>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">Roles & Permissions</h3>
          <button 
            className="btn btn-sm btn-primary"
            onClick={handleAddRole}
          >
            <i className="fas fa-plus mr-2"></i> Add Role
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {roles.map(role => (
            <div key={role.id} className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow transition-shadow">
              <div className="p-4">
                <div className="flex items-center mb-3">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${role.iconBg}`}>
                    <i className={`fas ${role.icon}`}></i>
                  </div>
                  <div className="ml-3">
                    <h4 className="text-base font-medium text-gray-900">{role.name}</h4>
                    <span className="text-xs text-gray-500">{role.userCount} users</span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mb-4">{role.description}</p>
                <button 
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                  onClick={() => handleEditRole(role)}
                >
                  Edit Permissions
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default UserSettings;