import { 
  LoginCredentials, 
  AuthResponse, 
  User, 
  ResetPasswordRequest, 
  ChangePasswordRequest,
  UserRole 
} from '../types';

// Mock users for demo purposes - organized by groups
const mockUsers: User[] = [
  // Carrier Group Users
  {
    id: '1',
    username: 'dispatcher1',
    firstName: '<PERSON>',
    lastName: 'Dispatcher',
    email: '<EMAIL>',
    role: UserRole.DISPATCHER,
    avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    phone: '(*************',
    department: 'Operations',
    lastLogin: '2025-05-20T08:30:00Z'
  },
  {
    id: '2',
    username: 'operator1',
    firstName: 'Sarah',
    lastName: 'Operator',
    email: '<EMAIL>',
    role: UserRole.OPERATOR,
    avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    phone: '(*************',
    department: 'Operations',
    lastLogin: '2025-05-19T14:45:00Z'
  },
  {
    id: '3',
    username: 'supervisor1',
    firstName: '<PERSON>',
    lastName: 'Supervisor',
    email: '<EMAIL>',
    role: UserRole.SUPERVISOR,
    avatar: 'https://randomuser.me/api/portraits/men/22.jpg',
    phone: '(*************',
    department: 'Management',
    lastLogin: '2025-05-20T07:15:00Z'
  },
  {
    id: '4',
    username: 'admin',
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    role: UserRole.ADMIN,
    avatar: 'https://randomuser.me/api/portraits/women/68.jpg',
    phone: '(*************',
    department: 'IT',
    lastLogin: '2025-05-20T09:00:00Z'
  },
  {
    id: '5',
    username: 'carrier1',
    firstName: 'Robert',
    lastName: 'Johnson',
    email: '<EMAIL>',
    role: UserRole.CARRIER,
    avatar: 'https://randomuser.me/api/portraits/men/45.jpg',
    phone: '(*************',
    department: 'Fleet Operations',
    lastLogin: '2025-05-21T09:00:00Z'
  },
  
  // Broker Group Users
  {
    id: '6',
    username: 'broker1',
    firstName: 'Emily',
    lastName: 'Anderson',
    email: '<EMAIL>',
    role: UserRole.BROKER,
    avatar: 'https://randomuser.me/api/portraits/women/33.jpg',
    phone: '(*************',
    department: 'Brokerage',
    lastLogin: '2025-05-21T10:15:00Z'
  },
  {
    id: '7',
    username: 'broker2',
    firstName: 'David',
    lastName: 'Martinez',
    email: '<EMAIL>',
    role: UserRole.BROKER,
    avatar: 'https://randomuser.me/api/portraits/men/52.jpg',
    phone: '(*************',
    department: 'Brokerage',
    lastLogin: '2025-05-21T11:30:00Z'
  },
  {
    id: '8',
    username: 'broker3',
    firstName: 'Jessica',
    lastName: 'Williams',
    email: '<EMAIL>',
    role: UserRole.BROKER,
    avatar: 'https://randomuser.me/api/portraits/women/45.jpg',
    phone: '(*************',
    department: 'Brokerage',
    lastLogin: '2025-05-21T13:45:00Z'
  },
  
  // Shipper Group Users
  {
    id: '9',
    username: 'shipper1',
    firstName: 'Tom',
    lastName: 'Shanahan',
    email: '<EMAIL>',
    role: UserRole.SHIPPER,
    avatar: 'https://randomuser.me/api/portraits/men/60.jpg',
    phone: '(*************',
    department: 'Logistics',
    lastLogin: '2025-05-21T14:00:00Z'
  },
  {
    id: '10',
    username: 'shipper2',
    firstName: 'Lisa',
    lastName: 'Chen',
    email: '<EMAIL>',
    role: UserRole.SHIPPER,
    avatar: 'https://randomuser.me/api/portraits/women/55.jpg',
    phone: '(*************',
    department: 'Supply Chain',
    lastLogin: '2025-05-21T15:30:00Z'
  },
  {
    id: '11',
    username: 'shipper3',
    firstName: 'Mark',
    lastName: 'Thompson',
    email: '<EMAIL>',
    role: UserRole.SHIPPER,
    avatar: 'https://randomuser.me/api/portraits/men/48.jpg',
    phone: '(*************',
    department: 'Operations',
    lastLogin: '2025-05-21T16:45:00Z'
  }
];

// Helper to simulate API call with delay
const simulateApiCall = <T>(data: T, delay = 800): Promise<T> => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (Math.random() > 0.05) { // 5% chance of failure for testing error handling
        resolve(data);
      } else {
        reject(new Error('Network error, please try again.'));
      }
    }, delay);
  });
};

// Local storage keys
const TOKEN_KEY = 'octopus_tms_token';
const USER_KEY = 'octopus_tms_user';

export const authService = {
  // Login function
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    // In a real app, this would make an API call to validate credentials
    const user = mockUsers.find(u => u.username === credentials.username);

    if (!user || credentials.password !== 'password') { // Simple mock password check
      return simulateApiCall(Promise.reject(new Error('Invalid username or password')));
    }

    const response: AuthResponse = {
      user,
      token: `mock-jwt-token-${Math.random().toString(36).substring(2, 15)}`,
      expiresAt: Date.now() + 24 * 60 * 60 * 1000 // 24 hours from now
    };

    // Store in localStorage if remember me is true
    if (credentials.rememberMe) {
      localStorage.setItem(TOKEN_KEY, response.token);
      localStorage.setItem(USER_KEY, JSON.stringify(user));
    } else {
      // Use session storage if not remembering
      sessionStorage.setItem(TOKEN_KEY, response.token);
      sessionStorage.setItem(USER_KEY, JSON.stringify(user));
    }

    return simulateApiCall(response);
  },

  // Logout function
  logout: async (): Promise<void> => {
    // In a real app, this might invalidate the token on the server
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(USER_KEY);
    sessionStorage.removeItem(TOKEN_KEY);
    sessionStorage.removeItem(USER_KEY);

    return simulateApiCall(undefined, 300);
  },

  // Check if a user is currently authenticated
  isAuthenticated: (): boolean => {
    const token = localStorage.getItem(TOKEN_KEY) || sessionStorage.getItem(TOKEN_KEY);
    return !!token;
  },

  // Get the current user if authenticated
  getCurrentUser: (): User | null => {
    try {
      const userStr = localStorage.getItem(USER_KEY) || sessionStorage.getItem(USER_KEY);
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('Error parsing user from storage:', error);
      return null;
    }
  },

  // Refresh token (for extending sessions)
  refreshToken: async (): Promise<string> => {
    const user = authService.getCurrentUser();
    if (!user) {
      return Promise.reject(new Error('No authenticated user'));
    }

    const newToken = `mock-jwt-token-${Math.random().toString(36).substring(2, 15)}`;
    const storageType = localStorage.getItem(TOKEN_KEY) ? localStorage : sessionStorage;

    storageType.setItem(TOKEN_KEY, newToken);

    return simulateApiCall(newToken);
  },

  // Request password reset
  requestPasswordReset: async (request: ResetPasswordRequest): Promise<void> => {
    const user = mockUsers.find(u => u.email === request.email);
    if (!user) {
      return simulateApiCall(Promise.reject(new Error('Email not found')));
    }

    // In a real app, this would send an email with a reset link
    console.log(`Password reset requested for ${request.email}`);
    return simulateApiCall(undefined);
  },

  // Change password
  changePassword: async (request: ChangePasswordRequest): Promise<void> => {
    // In a real app, this would verify the old password and update to the new one
    if (request.oldPassword !== 'password') {
      return simulateApiCall(Promise.reject(new Error('Current password is incorrect')));
    }

    console.log('Password changed successfully');
    return simulateApiCall(undefined);
  },

  // Update user profile
  updateProfile: async (userData: Partial<User>): Promise<User> => {
    const currentUser = authService.getCurrentUser();
    if (!currentUser) {
      return simulateApiCall(Promise.reject(new Error('No authenticated user')));
    }

    // Update user data
    const updatedUser = { ...currentUser, ...userData };

    // Update in storage
    const storageType = localStorage.getItem(USER_KEY) ? localStorage : sessionStorage;
    storageType.setItem(USER_KEY, JSON.stringify(updatedUser));

    return simulateApiCall(updatedUser);
  }
};
