import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card } from '../../../components';
import WeatherWidget from '../../../components/WeatherWidget';
import { RevenueChart, LoadVolumeChart } from '../../../components/charts';
import { fetchWeatherAlerts } from '../../../services/weatherService';
import type { WeatherAlert } from '../../../types';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [weatherAlerts, setWeatherAlerts] = useState<WeatherAlert[]>([]);
  const [weatherLoading, setWeatherLoading] = useState<boolean>(true);

  // Fetch real weather alerts
  useEffect(() => {
    const getWeatherAlerts = async () => {
      try {
        setWeatherLoading(true);
        const alerts = await fetchWeatherAlerts();
        setWeatherAlerts(alerts);
      } catch (error) {
        console.error('Error fetching weather alerts:', error);
      } finally {
        setWeatherLoading(false);
      }
    };

    getWeatherAlerts();

    // Refresh weather data every 30 minutes
    const intervalId = setInterval(getWeatherAlerts, 30 * 60 * 1000);

    return () => clearInterval(intervalId);
  }, []);

  // Mock data for charts and upcoming deliveries
  const revenuePerCustomer = [
    { customer: 'ABC Logistics', revenue: 125000, color: '#3B82F6' },
    { customer: 'XYZ Transport', revenue: 98000, color: '#10B981' },
    { customer: 'Global Freight', revenue: 87000, color: '#F59E0B' },
    { customer: 'Express Ship Co', revenue: 76000, color: '#8B5CF6' },
    { customer: 'Prime Movers', revenue: 65000, color: '#EF4444' }
  ];

  const weeklyLoadVolume = {
    dates: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    counts: [18, 22, 25, 20, 28, 15, 10]
  };

  const upcomingDeliveries = [
    { load: 'BL-1235', destination: 'Phoenix, AZ', eta: 'Today, 4:30 PM', driver: 'Mike Johnson', status: 'On Time' },
    { load: 'BL-1240', destination: 'Las Vegas, NV', eta: 'Today, 6:00 PM', driver: 'Sarah Williams', status: 'On Time' },
    { load: 'BL-1241', destination: 'San Francisco, CA', eta: 'Tomorrow, 10:00 AM', driver: 'John Davis', status: 'Delayed' },
    { load: 'BL-1242', destination: 'Portland, OR', eta: 'Tomorrow, 2:00 PM', driver: 'Emily Chen', status: 'On Time' }
  ];

  return (
    <div className="fade-in">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Broker Dashboard</h1>
        <p className="text-gray-600">Overview of your freight brokerage operations</p>
      </div>
      
      <section className="mb-8">
        <h2 className="flex items-center text-xl font-semibold mb-4">
          <i className="fas fa-chart-line mr-2 text-blue-600"></i>
          Key Metrics
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5">
          <div className="dashboard-card bg-gradient-blue">
            <div className="card-body">
              <i className="fas fa-truck-loading card-icon"></i>
              <h5 className="card-title text-gray-600">Open Loads</h5>
              <p className="text-3xl font-bold mt-2">24</p>
              <p className="text-sm text-gray-500 mt-1">5 awaiting carrier assignment</p>
              <div className="mt-3 bg-blue-200 bg-opacity-30 rounded-full h-1.5">
                <div className="bg-white h-1.5 rounded-full" style={{ width: '75%' }}></div>
              </div>
            </div>
          </div>
          
          <div className="dashboard-card bg-gradient-green">
            <div className="card-body">
              <i className="fas fa-dollar-sign card-icon"></i>
              <h5 className="card-title text-gray-600">Pending Payments</h5>
              <p className="text-3xl font-bold mt-2">$12,450</p>
              <p className="text-sm text-gray-500 mt-1">8 invoices pending approval</p>
              <div className="mt-3 bg-green-200 bg-opacity-30 rounded-full h-1.5">
                <div className="bg-white h-1.5 rounded-full" style={{ width: '60%' }}></div>
              </div>
            </div>
          </div>
          
          <div className="dashboard-card bg-gradient-purple">
            <div className="card-body">
              <i className="fas fa-chart-bar card-icon"></i>
              <h5 className="card-title text-gray-600">Total Revenue (MTD)</h5>
              <p className="text-3xl font-bold mt-2">$45,890</p>
              <p className="text-sm text-gray-500 mt-1">+12% from last month</p>
              <div className="mt-3 bg-purple-200 bg-opacity-30 rounded-full h-1.5">
                <div className="bg-white h-1.5 rounded-full" style={{ width: '85%' }}></div>
              </div>
            </div>
          </div>

          <div className="dashboard-card bg-gradient-orange">
            <div className="card-body">
              <i className="fas fa-handshake card-icon"></i>
              <h5 className="card-title text-gray-600">Active Carriers</h5>
              <p className="text-3xl font-bold mt-2">45</p>
              <p className="text-sm text-gray-500 mt-1">12 preferred partners</p>
              <div className="mt-3 bg-orange-200 bg-opacity-30 rounded-full h-1.5">
                <div className="bg-white h-1.5 rounded-full" style={{ width: '90%' }}></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Actions */}
      <section className="mb-8">
        <h2 className="flex items-center text-xl font-semibold mb-4">
          <i className="fas fa-bolt mr-2 text-blue-600"></i>
          Quick Actions
        </h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button
            onClick={() => navigate('/broker/create-load')}
            className="flex flex-col items-center justify-center p-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:bg-blue-50 hover:border-blue-200 transition-all duration-200 transform hover:scale-105"
          >
            <i className="fas fa-plus-circle text-blue-500 text-3xl mb-2"></i>
            <span className="text-sm font-medium">Create Load</span>
          </button>
          <button
            onClick={() => navigate('/broker/smart-load-match')}
            className="flex flex-col items-center justify-center p-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:bg-green-50 hover:border-green-200 transition-all duration-200 transform hover:scale-105"
          >
            <i className="fas fa-magic text-green-500 text-3xl mb-2"></i>
            <span className="text-sm font-medium">Smart Match</span>
          </button>
          <button
            onClick={() => navigate('/broker/carrier-match')}
            className="flex flex-col items-center justify-center p-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:bg-purple-50 hover:border-purple-200 transition-all duration-200 transform hover:scale-105"
          >
            <i className="fas fa-handshake text-purple-500 text-3xl mb-2"></i>
            <span className="text-sm font-medium">Carrier Match</span>
          </button>
          <button
            onClick={() => navigate('/broker/reports')}
            className="flex flex-col items-center justify-center p-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:bg-orange-50 hover:border-orange-200 transition-all duration-200 transform hover:scale-105"
          >
            <i className="fas fa-chart-bar text-orange-500 text-3xl mb-2"></i>
            <span className="text-sm font-medium">Reports</span>
          </button>
        </div>
      </section>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <section>
          <h2 className="flex items-center text-xl font-semibold mb-4">
            <i className="fas fa-chart-bar mr-2 text-blue-600"></i>
            Revenue per Customer
          </h2>
          <Card className="h-full">
            <div className="w-full h-full p-4">
              <RevenueChart data={revenuePerCustomer} />
            </div>
          </Card>
        </section>

        <section>
          <h2 className="flex items-center text-xl font-semibold mb-4">
            <i className="fas fa-calendar-alt mr-2 text-blue-600"></i>
            Weekly Load Volume
          </h2>
          <Card className="h-full">
            <div className="w-full h-full p-4">
              <LoadVolumeChart data={weeklyLoadVolume} />
            </div>
          </Card>
        </section>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Load Status Distribution</h2>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium text-gray-700">Posted</span>
                  <span className="text-sm font-medium text-gray-700">12</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div className="bg-blue-600 h-2.5 rounded-full" style={{ width: '30%' }}></div>
                </div>
              </div>
              
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium text-gray-700">Assigned</span>
                  <span className="text-sm font-medium text-gray-700">8</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div className="bg-green-600 h-2.5 rounded-full" style={{ width: '20%' }}></div>
                </div>
              </div>
              
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium text-gray-700">En Route</span>
                  <span className="text-sm font-medium text-gray-700">15</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div className="bg-yellow-600 h-2.5 rounded-full" style={{ width: '37.5%' }}></div>
                </div>
              </div>
              
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm font-medium text-gray-700">Delivered</span>
                  <span className="text-sm font-medium text-gray-700">5</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div className="bg-purple-600 h-2.5 rounded-full" style={{ width: '12.5%' }}></div>
                </div>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Recent Loads</h2>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Load #
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Route
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Carrier
                    </th>
                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  <tr>
                    <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">BL-1234</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">Chicago, IL → Atlanta, GA</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">FastFreight Inc.</td>
                    <td className="px-3 py-2 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        Assigned
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">BL-1235</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">Dallas, TX → Phoenix, AZ</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">Southwest Carriers</td>
                    <td className="px-3 py-2 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                        En Route
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">BL-1236</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">Miami, FL → Charlotte, NC</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">East Coast Logistics</td>
                    <td className="px-3 py-2 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                        Posted
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td className="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">BL-1237</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">Seattle, WA → Portland, OR</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">Pacific Transport</td>
                    <td className="px-3 py-2 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                        Delivered
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div className="mt-4 text-right">
              <a href="/broker/loads" className="text-sm font-medium text-blue-600 hover:text-blue-800">
                View all loads →
              </a>
            </div>
          </div>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 mb-8">
        <div className="lg:col-span-8">
          <section>
            <h2 className="flex items-center text-xl font-semibold mb-4">
              <i className="fas fa-truck mr-2 text-blue-600"></i>
              Upcoming Deliveries
            </h2>
            <Card>
              <ul className="divide-y divide-gray-200">
                {upcomingDeliveries.map((delivery, index) => {
                  const isDelayed = delivery.status === 'Delayed';
                  const statusClass = isDelayed ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800';

                  return (
                    <li key={index} className="py-4 px-6 hover:bg-gray-50 transition-colors">
                      <div className="flex items-start">
                        <div className={`flex-shrink-0 h-10 w-10 rounded-full flex items-center justify-center ${
                          isDelayed ? 'bg-red-100 text-red-600' : 'bg-green-100 text-green-600'
                        }`}>
                          <i className={`fas ${isDelayed ? 'fa-exclamation-circle' : 'fa-check-circle'}`}></i>
                        </div>
                        <div className="ml-4 flex-1">
                          <div className="flex justify-between">
                            <h3 className="text-sm font-medium">{delivery.load}</h3>
                            <span className={`${statusClass} px-2 py-1 rounded-full text-xs font-medium`}>
                              {delivery.status}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600">{delivery.destination}</p>
                          <div className="mt-1 text-xs text-gray-500">
                            <div className="flex items-center mb-1">
                              <i className="fas fa-clock mr-1"></i>
                              <span>ETA: {delivery.eta}</span>
                            </div>
                            <div className="flex items-center">
                              <i className="fas fa-user mr-1"></i>
                              <span>{delivery.driver}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  );
                })}
              </ul>
            </Card>
          </section>
        </div>

        <div className="lg:col-span-4">
          <section className="mb-6">
            <h2 className="flex items-center text-xl font-semibold mb-4">
              <i className="fas fa-cloud-sun mr-2 text-blue-600"></i>
              Current Weather
            </h2>
            <WeatherWidget 
              lat={40.4406}
              lon={-79.9959}
              location="Pittsburgh, PA"
            />
          </section>

          <section>
            <h2 className="flex items-center text-xl font-semibold mb-4">
              <i className="fas fa-cloud-sun-rain mr-2 text-blue-600"></i>
              Weather Alerts
            </h2>
            <Card>
              {weatherLoading ? (
                <div className="flex justify-center items-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                </div>
              ) : weatherAlerts.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  <i className="fas fa-sun text-yellow-400 text-3xl mb-2"></i>
                  <p>No weather alerts at this time.</p>
                </div>
              ) : (
                <div className="space-y-3 p-4">
                  {weatherAlerts.map((alert, index) => {
                    const impactClass = alert.impact === 'High' 
                      ? 'bg-red-50 border-red-400 text-red-800' 
                      : alert.impact === 'Medium' 
                        ? 'bg-yellow-50 border-yellow-400 text-yellow-800' 
                        : 'bg-blue-50 border-blue-400 text-blue-800';

                    const impactIcon = alert.impact === 'High' 
                      ? 'fa-exclamation-triangle' 
                      : alert.impact === 'Medium' 
                        ? 'fa-exclamation-circle' 
                        : 'fa-info-circle';

                    return (
                      <div key={index} className={`${impactClass} border-l-4 p-4 rounded-md`}>
                        <div className="flex">
                          <div className="flex-shrink-0">
                            <i className={`fas ${impactIcon}`}></i>
                          </div>
                          <div className="ml-3">
                            <h3 className="text-sm font-medium">
                              <span className="font-bold">{alert.region}:</span> {alert.alert}
                            </h3>
                            <div className="mt-1 text-xs">
                              <span className="font-semibold">Impact: </span>{alert.impact}
                              <span className="mx-1">&bull;</span>
                              <span className="font-semibold">Loads Affected: </span>{alert.affectedLoads}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}

                  <div className="text-xs text-gray-500 text-right mt-2">
                    <i className="fas fa-sync-alt mr-1"></i> Weather data refreshes every 30 minutes
                  </div>
                </div>
              )}
            </Card>
          </section>
        </div>
      </div>
      
      <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
        <button className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md shadow-sm flex items-center justify-center">
          <i className="fas fa-plus-circle mr-2"></i> Create New Load
        </button>
        <button className="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md shadow-sm flex items-center justify-center">
          <i className="fas fa-search mr-2"></i> Find Carriers
        </button>
        <button className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-md shadow-sm flex items-center justify-center">
          <i className="fas fa-file-invoice-dollar mr-2"></i> Manage Payments
        </button>
      </div>
    </div>
  );
};

export default Dashboard;