@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
@layer base {
  html {
    font-family: 'Poppins', sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-800;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold text-gray-900;
  }
  
  h2 {
    @apply text-xl mb-4 pb-2 border-b border-gray-200;
  }
  
  h3 {
    @apply text-lg mb-3;
  }
}

/* Custom components */
@layer components {
  /* Cards */
  .dashboard-card {
    @apply rounded-xl shadow-lg overflow-hidden transition-all duration-300 h-full;
  }
  
  .dashboard-card:hover {
    @apply shadow-xl transform -translate-y-1;
  }
  
  .card-body {
    @apply p-5;
  }
  
  .card-icon {
    @apply text-4xl opacity-80 mb-3;
  }
  
  .card-title {
    @apply text-lg font-medium mb-1;
  }
  
  /* Status badges */
  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-badge-booked {
    @apply bg-gray-100 text-gray-800;
  }
  
  .status-badge-assigned {
    @apply bg-blue-100 text-blue-800;
  }
  
  .status-badge-picked-up {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .status-badge-delivered {
    @apply bg-green-100 text-green-800;
  }
  
  /* Buttons */
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }
  
  .btn-secondary {
    @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }
  
  .btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }
  
  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  .btn-outline-primary {
    @apply border border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500;
  }
  
  .btn-outline-secondary {
    @apply border border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500;
  }
  
  .btn-outline-success {
    @apply border border-green-600 text-green-600 hover:bg-green-50 focus:ring-green-500;
  }
  
  .btn-outline-danger {
    @apply border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500;
  }
  
  .btn-sm {
    @apply px-3 py-1 text-sm rounded;
  }
  
  /* Form controls */
  .form-control {
    @apply block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
  }
  
  .form-select {
    @apply block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
  
  /* Load cards for the dispatch board */
  .load-card {
    @apply bg-white p-3 mb-2 rounded-md border border-gray-200 shadow-sm hover:shadow transition-all;
  }
}

/* Utility classes */
@layer utilities {
  /* Gradient backgrounds */
  .bg-gradient-blue {
    @apply bg-gradient-to-br from-blue-500 to-blue-700 text-white;
  }
  
  .bg-gradient-green {
    @apply bg-gradient-to-br from-green-500 to-green-700 text-white;
  }
  
  .bg-gradient-orange {
    @apply bg-gradient-to-br from-orange-400 to-orange-600 text-white;
  }
  
  .bg-gradient-purple {
    @apply bg-gradient-to-br from-purple-500 to-purple-700 text-white;
  }
  
  .bg-gradient-red {
    @apply bg-gradient-to-br from-red-500 to-red-700 text-white;
  }
  
  .bg-gradient-teal {
    @apply bg-gradient-to-br from-teal-400 to-teal-600 text-white;
  }
  
  .bg-gradient-indigo {
    @apply bg-gradient-to-br from-indigo-500 to-indigo-700 text-white;
  }
  
  .bg-gradient-cyan {
    @apply bg-gradient-to-br from-cyan-400 to-cyan-600 text-white;
  }
  
  /* Animations */
  .fade-in {
    animation: fadeIn 0.5s ease-out forwards;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Scrollbar styling */
  .scrollbar-thin {
    scrollbar-width: thin;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded-full;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-gray-400 rounded-full hover:bg-gray-500;
  }
  
  /* Transitions */
  .transition-fast {
    @apply transition-all duration-200 ease-in-out;
  }
  
  .transition-normal {
    @apply transition-all duration-300 ease-in-out;
  }
  
  .transition-slow {
    @apply transition-all duration-500 ease-in-out;
  }
  
  /* Animation delays */
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  
  .animation-delay-400 {
    animation-delay: 400ms;
  }
  
  /* Pulsing phone animation */
  @keyframes phone-ring {
    0%, 100% {
      transform: rotate(0deg);
    }
    10%, 30% {
      transform: rotate(-10deg);
    }
    20%, 40% {
      transform: rotate(10deg);
    }
  }
  
  .animate-phone-ring {
    animation: phone-ring 2s ease-in-out infinite;
  }
}

/* Mapbox Geocoder Styles */
.mapboxgl-ctrl-geocoder {
  @apply w-full;
  min-width: 100%;
  max-width: 100%;
}

.mapboxgl-ctrl-geocoder--input {
  @apply w-full px-10 py-2.5 text-gray-900 placeholder-gray-400 bg-white border border-gray-300 rounded-md shadow-sm;
  @apply focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 focus:outline-none;
  font-family: 'Poppins', system-ui, sans-serif;
  font-size: 14px;
}

.mapboxgl-ctrl-geocoder--icon-search {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400;
}

.mapboxgl-ctrl-geocoder--button {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2;
  background: none;
  border: none;
  padding: 4px;
}

.mapboxgl-ctrl-geocoder--icon-close {
  @apply w-4 h-4 text-gray-400 hover:text-gray-600;
}

.mapboxgl-ctrl-geocoder--suggestions {
  @apply absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden;
  max-height: 300px;
  overflow-y: auto;
}

.mapboxgl-ctrl-geocoder--suggestion {
  @apply px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0;
}

.mapboxgl-ctrl-geocoder--suggestion-title {
  @apply text-gray-900 font-medium text-sm;
}

.mapboxgl-ctrl-geocoder--suggestion-address {
  @apply text-xs text-gray-500 mt-0.5;
}

.mapboxgl-ctrl-geocoder--powered-by {
  @apply hidden;
}