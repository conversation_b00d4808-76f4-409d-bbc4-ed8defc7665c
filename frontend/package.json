{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@mapbox/search-js-core": "^1.1.0", "@mapbox/search-js-react": "^1.1.0", "@react-pdf/renderer": "^3.1.14", "@types/leaflet": "^1.9.8", "@xyflow/react": "^12.6.4", "apexcharts": "^3.44.0", "axios": "^1.6.2", "leaflet": "^1.9.4", "lucide-react": "^0.511.0", "mapbox-gl": "^3.12.0", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-dom": "^18.2.0", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.4.1", "react-leaflet": "^4.2.1", "react-pdf": "^7.5.1", "react-router-dom": "^6.15.0", "react-toastify": "^11.0.5", "reactflow": "^11.11.4", "recharts": "^2.15.3", "zod": "^3.25.32", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^2.1.4", "@eslint/js": "^8.47.0", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "globals": "^13.20.0", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "typescript": "^5.0.4", "vite": "^4.4.5"}}