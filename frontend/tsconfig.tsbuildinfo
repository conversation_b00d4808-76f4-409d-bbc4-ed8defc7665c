{"root": ["./tailwind.config.ts", "./vite.config.ts", "./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/documentviewer.tsx", "./src/components/errorboundary.tsx", "./src/components/sidebar.tsx", "./src/components/toggleswitch.tsx", "./src/components/weatherwidget.tsx", "./src/components/index.ts", "./src/components/charts/loadvolumechart.tsx", "./src/components/charts/revenuechart.tsx", "./src/components/charts/index.ts", "./src/components/dashboard/kpicards.tsx", "./src/components/dashboard/quickactions.tsx", "./src/components/dashboard/systemalerts.tsx", "./src/components/dashboard/viewcontrols.tsx", "./src/components/dashboard/weatheralerts.tsx", "./src/components/dashboard/index.ts", "./src/components/filters/searchbar.tsx", "./src/components/filters/statusfilter.tsx", "./src/components/filters/index.ts", "./src/components/forms/announcementform.tsx", "./src/components/forms/customeroutreachform.tsx", "./src/components/layout/topbar.tsx", "./src/components/loaders/searchloader.tsx", "./src/components/loaders/index.ts", "./src/components/modals/index.ts", "./src/components/settings/appearancesettings.tsx", "./src/components/settings/backupsettings.tsx", "./src/components/settings/billingsettings.tsx", "./src/components/settings/companysettings.tsx", "./src/components/settings/integrationsettings.tsx", "./src/components/settings/logsettings.tsx", "./src/components/settings/notificationsettings.tsx", "./src/components/settings/usersettings.tsx", "./src/components/tables/datatable.tsx", "./src/components/tables/driverperformancetable.tsx", "./src/components/tables/fieldoperationstable.tsx", "./src/components/tables/teamworkloadtable.tsx", "./src/components/tables/index.ts", "./src/components/ui/card.tsx", "./src/components/ui/modal.tsx", "./src/components/ui/table.tsx", "./src/context/authcontext.tsx", "./src/data/alerts.ts", "./src/data/companyprofile.ts", "./src/data/dashboard.ts", "./src/data/dispatch-loads.ts", "./src/data/documents.ts", "./src/data/drivers.ts", "./src/data/invoices.ts", "./src/data/kpis.ts", "./src/data/loadboards.ts", "./src/data/loads.ts", "./src/data/operations.ts", "./src/data/reports.ts", "./src/data/shipments.ts", "./src/data/teamworkload.ts", "./src/data/tracking-shipments.ts", "./src/data/users.ts", "./src/hooks/index.ts", "./src/hooks/useloadmatcher.tsx", "./src/hooks/usesupervisordashboard.ts", "./src/layouts/mainlayout.tsx", "./src/layouts/workflowbuilderlayout.tsx", "./src/layouts/index.ts", "./src/modules/broker/brokerroutes.tsx", "./src/modules/broker/api/brokerapi.ts", "./src/modules/broker/components/carriercallmodal.tsx", "./src/modules/broker/components/carriermatchmodal.tsx", "./src/modules/broker/components/loadcard.tsx", "./src/modules/broker/components/loadprogresstracker.tsx", "./src/modules/broker/components/loadstatusbadge.tsx", "./src/modules/broker/components/ratebenchmarktooltip.tsx", "./src/modules/broker/components/searchloader.tsx", "./src/modules/broker/hooks/usebrokerdashboard.ts", "./src/modules/broker/hooks/usecarriermatch.ts", "./src/modules/broker/hooks/usecarriermatcher.ts", "./src/modules/broker/hooks/usecarriers.ts", "./src/modules/broker/hooks/usecontracts.ts", "./src/modules/broker/hooks/usecustomers.ts", "./src/modules/broker/hooks/usedocuments.ts", "./src/modules/broker/hooks/useloadslist.ts", "./src/modules/broker/hooks/usepayments.ts", "./src/modules/broker/hooks/usereports.ts", "./src/modules/broker/pages/carriermatch.tsx", "./src/modules/broker/pages/carriers.tsx", "./src/modules/broker/pages/commissions.tsx", "./src/modules/broker/pages/contracts.tsx", "./src/modules/broker/pages/createload.tsx", "./src/modules/broker/pages/customers.tsx", "./src/modules/broker/pages/dashboard.tsx", "./src/modules/broker/pages/documents.tsx", "./src/modules/broker/pages/loads.tsx", "./src/modules/broker/pages/payments.tsx", "./src/modules/broker/pages/reports.tsx", "./src/modules/broker/pages/smartloadmatch.tsx", "./src/modules/broker/pages/supervisordashboard.tsx", "./src/modules/broker/pages/tracking.tsx", "./src/modules/broker/pages/workflowbuilder.tsx", "./src/modules/broker/pages/workflows.tsx", "./src/modules/carrier/carrierroutes.tsx", "./src/modules/carrier/api/carrierapi.ts", "./src/modules/carrier/components/drivercard.tsx", "./src/modules/carrier/components/loadcard.tsx", "./src/modules/carrier/components/routedisplay.tsx", "./src/modules/carrier/components/statusbadge.tsx", "./src/modules/carrier/components/index.ts", "./src/modules/carrier/hooks/useloadmatcher.tsx", "./src/modules/carrier/pages/allloads.tsx", "./src/modules/carrier/pages/dashboard.tsx", "./src/modules/carrier/pages/dispatchboard.tsx", "./src/modules/carrier/pages/documents.tsx", "./src/modules/carrier/pages/drivers.tsx", "./src/modules/carrier/pages/invoices.tsx", "./src/modules/carrier/pages/reports.tsx", "./src/modules/carrier/pages/smartloadsearch.tsx", "./src/modules/carrier/pages/tracking.tsx", "./src/modules/carrier/pages/workflowbuilder.tsx", "./src/modules/carrier/pages/workflows.tsx", "./src/modules/shared/components/brokercallmodal.tsx", "./src/modules/shared/components/loadmatchmodal.tsx", "./src/modules/shared/components/index.ts", "./src/modules/shared/workflows/components/nodepalette.tsx", "./src/modules/shared/workflows/components/workflowbuilder.tsx", "./src/modules/shared/workflows/components/workflowbuilderwrapper.tsx", "./src/modules/shared/workflows/components/workflowheader.tsx", "./src/modules/shared/workflows/components/workflowlist.tsx", "./src/modules/shared/workflows/components/nodes/actionnode.tsx", "./src/modules/shared/workflows/components/nodes/conditionnode.tsx", "./src/modules/shared/workflows/components/nodes/delaynode.tsx", "./src/modules/shared/workflows/components/nodes/triggernode.tsx", "./src/modules/shared/workflows/components/nodes/index.ts", "./src/modules/shared/workflows/components/panels/actionconfigpanel.tsx", "./src/modules/shared/workflows/components/panels/conditionconfigpanel.tsx", "./src/modules/shared/workflows/components/panels/delayconfigpanel.tsx", "./src/modules/shared/workflows/components/panels/nodeconfigpanel.tsx", "./src/modules/shared/workflows/components/panels/triggerconfigpanel.tsx", "./src/modules/shared/workflows/store/workflowstore.ts", "./src/modules/shared/workflows/templates/brokertemplates.ts", "./src/modules/shared/workflows/templates/carriertemplates.ts", "./src/modules/shared/workflows/templates/index.ts", "./src/modules/shared/workflows/templates/shippertemplates.ts", "./src/modules/shared/workflows/types/workflow.types.ts", "./src/modules/shared/workflows/utils/nodehelpers.ts", "./src/modules/shared/workflows/utils/validation.ts", "./src/modules/shipper/shipperroutes.tsx", "./src/modules/shipper/api/wmsapi.ts", "./src/modules/shipper/components/wms/inventoryalertcard.tsx", "./src/modules/shipper/components/wms/inventoryitemcard.tsx", "./src/modules/shipper/components/wms/readinessnotificationmodal.tsx", "./src/modules/shipper/components/wms/shipmentreadinesscard.tsx", "./src/modules/shipper/components/wms/warehouseoverview.tsx", "./src/modules/shipper/components/wms/index.ts", "./src/modules/shipper/pages/createload.tsx", "./src/modules/shipper/pages/dashboard.tsx", "./src/modules/shipper/pages/documents.tsx", "./src/modules/shipper/pages/loads.tsx", "./src/modules/shipper/pages/reports.tsx", "./src/modules/shipper/pages/settings.tsx", "./src/modules/shipper/pages/tracking.tsx", "./src/modules/shipper/pages/warehousedashboard.tsx", "./src/modules/shipper/pages/workflowbuilder.tsx", "./src/modules/shipper/pages/workflows.tsx", "./src/modules/shipper/types/wms.types.ts", "./src/pages/forgotpassword.tsx", "./src/pages/loaddetails.tsx", "./src/pages/login.tsx", "./src/pages/profile.tsx", "./src/pages/reports.tsx", "./src/pages/settings.tsx", "./src/pages/supervisordashboard.tsx", "./src/pages/index.ts", "./src/routes/index.tsx", "./src/services/api.ts", "./src/services/authservice.ts", "./src/services/documentservice.ts", "./src/services/index.ts", "./src/services/mockactions.ts", "./src/services/weatherservice.ts", "./src/types/index.ts", "./src/types/settings.ts", "./src/types/user.ts", "./src/utils/validation.ts"], "version": "5.8.3"}